# Chonkie 库编码问题修复

## 问题描述

在使用 Chonkie 库处理中文文本时，遇到了编码问题。错误信息如下：

```
UnicodeDecodeError: 'gbk' codec can't decode byte 0x82 in position 270: illegal multibyte sequence
```

这是因为 Chonkie 库在读取 Hugging Face 上的配方文件时，使用了系统默认编码（在中文 Windows 系统上通常是 GBK），而不是 UTF-8 编码。

## 修复方法

修改了 Chonkie 库中的 `hub.py` 文件，在以下两个函数中添加了 `encoding='utf-8'` 参数：

1. `get_recipe` 函数：

```python
# 修改前
with path_obj.open("r") as f:
    recipe = dict(json.loads(f.read()))

# 修改后
with path_obj.open("r", encoding='utf-8') as f:
    recipe = dict(json.loads(f.read()))
```

2. `get_recipe_schema` 函数：

```python
# 修改前
with Path(path).open("r") as f:
    return dict(json.loads(f.read()))

# 修改后
with Path(path).open("r", encoding='utf-8') as f:
    return dict(json.loads(f.read()))
```

## 修复位置

文件路径：`.venv\Lib\site-packages\chonkie\utils\hub.py`

## 为什么需要这个修复

Chonkie 库从 Hugging Face 下载的配方文件是 UTF-8 编码的，但在 Windows 中文系统上，Python 默认使用 GBK 编码打开文件。由于 UTF-8 编码的中文字符在 GBK 编码中是非法的，所以会导致解码错误。

通过显式指定 `encoding='utf-8'`，我们确保了文件以正确的编码方式打开，从而解决了这个问题。

## 其他可能的解决方案

1. 创建本地的配方文件，而不是从 Hugging Face 下载
2. 直接创建 `RecursiveChunker` 实例，不使用 `from_recipe` 方法
3. 设置环境变量 `PYTHONIOENCODING=utf-8`

但最直接和有效的方法是修改 `hub.py` 文件，添加正确的编码参数。
