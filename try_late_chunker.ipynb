from chonkie import RecursiveChunker, RecursiveRules
from chonkie import LateChunker, OverlapRefinery

# 初始化LateChunker
late_chunker = LateChunker(
    embedding_model="all-MiniLM-L6-v2",
    chunk_size=2048,
    rules=RecursiveRules(),
    min_characters_per_chunk=24,
)

# 初始化OverlapRefinery来添加overlap
overlap_refinery = OverlapRefinery(
    tokenizer_or_token_counter="character",  # 使用字符计数
    context_size=0.15,                       # 15%的overlap（推荐范围10-20%）
    method="suffix",                         # 从下一个chunk添加上下文到当前chunk末尾
    merge=True,                             # 直接合并到chunk.text中
    inplace=False                           # 返回新的chunk列表
)

from chonkie import RecursiveChunker

# Initialize the recursive chunker to chunk Chinese texts
recursive_chunker = RecursiveChunker.from_recipe(lang="zh")

# 获取文本
file_path = r"C:\Users\<USER>\Desktop\ai_mindmap\documents\python第八章.md"

with open(file_path, 'r', encoding='utf-8') as file:
        text = file.read()
    

# 首先用LateChunker进行分块
chunks = late_chunker.chunk(text)

# 然后使用OverlapRefinery添加overlap
refined_chunks = overlap_refinery(chunks)

print(f"原始chunks数量: {len(chunks)}")
print(f"添加overlap后chunks数量: {len(refined_chunks)}")
print("\n=== 带overlap的chunks ===")

for i, chunk in enumerate(refined_chunks):
    print(f"\nChunk {i+1}:")
    print(f"Token count: {chunk.token_count}")
    print(f"Chunk Text: {chunk.text}...")

# 然后用recursive_chunker进行分块
chunks = late_chunker.chunk(text)

# 然后使用OverlapRefinery添加overlap
refined_chunks = overlap_refinery(chunks)

print(f"原始chunks数量: {len(chunks)}")
print(f"添加overlap后chunks数量: {len(refined_chunks)}")
print("\n=== 带overlap的chunks ===")

for i, chunk in enumerate(refined_chunks):
    print(f"\nChunk {i+1}:")
    print(f"Token count: {chunk.token_count}")
    print(f"Chunk Text: {chunk.text}...")

from chonkie import SemanticChunker

# Basic initialization with default parameters
semantic_chunker = SemanticChunker(
    embedding_model="minishlab/potion-base-8M",  # Default model
    threshold=0.5,                               # Similarity threshold (0-1) or (1-100) or "auto"
    chunk_size=2048,                              # Maximum tokens per chunk
    min_sentences=1                              # Initial sentences per chunk
)

# 然后用semantic进行分块
chunks = semantic_chunker.chunk(text)

# 然后使用OverlapRefinery添加overlap
refined_chunks = overlap_refinery(chunks)

print(f"原始chunks数量: {len(chunks)}")
print(f"添加overlap后chunks数量: {len(refined_chunks)}")
print("\n=== 带overlap的chunks ===")

for i, chunk in enumerate(refined_chunks):
    print(f"\nChunk {i+1}:")
    print(f"Token count: {chunk.token_count}")
    print(f"Chunk Text: {chunk.text}...")

# 安装依赖 (如果需要)
# !pip install "chonkie[neural]"

from chonkie import NeuralChunker

# 初始化Neural Chunker
neural_chunker = NeuralChunker(
    model="mirth/chonky_modernbert_base_1",  # 默认模型
    device_map="cpu",                        # 设备选择 ('cpu', 'cuda', 'mps')
    min_characters_per_chunk=10,             # 最小字符数
)

print("Neural Chunker 初始化完成！")

# 然后用recursive_chunker进行分块
chunks = neural_chunker.chunk(text)

# 然后使用OverlapRefinery添加overlap
refined_chunks = overlap_refinery(chunks)

print(f"原始chunks数量: {len(chunks)}")
print(f"添加overlap后chunks数量: {len(refined_chunks)}")
print("\n=== 带overlap的chunks ===")

for i, chunk in enumerate(refined_chunks):
    print(f"\nChunk {i+1}:")
    print(f"Token count: {chunk.token_count}")
    print(f"Chunk Text: {chunk.text}...")
    print("-" * 50)

