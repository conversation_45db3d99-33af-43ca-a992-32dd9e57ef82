#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试SDPM Chunker的分块行为
"""

from chonkie import SDPMChunker, TokenChunker
import tiktoken

def analyze_chunking_behavior(text):
    """分析分块行为"""
    
    print("🔍 分析SDPM Chunker的分块行为")
    print("=" * 60)
    
    # 初始化tokenizer来计算token
    encoding = tiktoken.get_encoding("cl100k_base")
    
    # 分析原始文本
    total_tokens = len(encoding.encode(text))
    sentences = text.split('。')  # 简单的句子分割
    
    print(f"📄 原始文本信息:")
    print(f"   总字符数: {len(text)}")
    print(f"   总token数: {total_tokens}")
    print(f"   句子数: {len(sentences)}")
    print(f"   平均每句token数: {total_tokens/len(sentences):.1f}")
    
    # 检查最长的句子
    max_sentence_tokens = 0
    longest_sentence = ""
    for sentence in sentences:
        if sentence.strip():
            tokens = len(encoding.encode(sentence))
            if tokens > max_sentence_tokens:
                max_sentence_tokens = tokens
                longest_sentence = sentence.strip()
    
    print(f"   最长句子token数: {max_sentence_tokens}")
    print(f"   最长句子内容: {longest_sentence[:100]}...")
    
    print("\n" + "=" * 60)
    
    # 测试不同的chunker配置
    configs = [
        {"name": "SDPM-宽松", "chunker": SDPMChunker(chunk_size=200, threshold=0.5, skip_window=1)},
        {"name": "SDPM-严格", "chunker": SDPMChunker(chunk_size=200, threshold=0.3, skip_window=0)},
        {"name": "Token-严格", "chunker": TokenChunker(chunk_size=200, chunk_overlap=20)}
    ]
    
    for config in configs:
        print(f"\n🔧 测试配置: {config['name']}")
        print("-" * 40)
        
        try:
            chunks = config['chunker'].chunk(text)
            
            print(f"   生成分块数: {len(chunks)}")
            
            for i, chunk in enumerate(chunks, 1):
                chunk_tokens = len(encoding.encode(chunk.text))
                print(f"   分块 {i}: {chunk_tokens} tokens")
                if chunk_tokens > 200:
                    print(f"     ⚠️  超过限制! 内容: {chunk.text[:100]}...")
                    
                    # 分析这个分块包含的句子
                    chunk_sentences = chunk.text.split('。')
                    print(f"     📊 包含句子数: {len(chunk_sentences)}")
                    for j, sent in enumerate(chunk_sentences[:3], 1):  # 只显示前3句
                        if sent.strip():
                            sent_tokens = len(encoding.encode(sent))
                            print(f"       句子{j}: {sent_tokens} tokens - {sent.strip()[:50]}...")
                            
        except Exception as e:
            print(f"   ❌ 错误: {e}")

def test_with_your_text():
    """测试你的具体文本"""
    
    # 这里放入你在notebook中使用的文本
    # 你可以把你的文本复制到这里
    test_text = """
    请把你在notebook中使用的文本粘贴到这里，
    这样我们就能具体分析为什么会出现780 token的分块了。
    """
    
    if "请把你在notebook" in test_text:
        print("⚠️  请在test_text变量中放入你的实际文本内容")
        return
    
    analyze_chunking_behavior(test_text)

if __name__ == "__main__":
    # 使用示例文本进行测试
    sample_text = """
    人工智能是计算机科学的一个分支，它试图理解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
    
    机器学习是人工智能的一个重要分支，它是一种通过算法解析数据、从中学习，然后对真实世界中的事件做出决策和预测的技术。机器学习算法会根据输入的训练数据建立模型，并利用该模型对新的数据进行预测或决策，而无需进行明确的程序编程。机器学习已经有了十分广泛的应用，例如数据挖掘、计算机视觉、自然语言处理、生物特征识别、搜索引擎、医学诊断、检测信用卡欺诈、证券市场分析、DNA序列测序、语音和手写识别、战略游戏和机器人等。
    
    深度学习是机器学习的一个子集，它模拟人脑的神经网络结构，通过多层神经网络来学习数据的表示。深度学习在图像识别、语音识别、自然语言处理等领域取得了突破性的进展。深度学习模型通常包含多个隐藏层，每一层都会对输入数据进行特征提取和转换，最终输出预测结果。深度学习的成功很大程度上依赖于大量的训练数据和强大的计算能力。
    """
    
    print("🧪 使用示例文本进行测试")
    analyze_chunking_behavior(sample_text)
    
    print("\n" + "="*60)
    print("💡 要分析你的具体文本，请:")
    print("1. 修改 test_with_your_text() 函数中的 test_text 变量")
    print("2. 调用 test_with_your_text() 函数")
