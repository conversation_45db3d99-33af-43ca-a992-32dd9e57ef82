<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI思维导图 - XMind风格</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
      body {
        margin: 0;
        padding: 20px;
        font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      .container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        padding: 20px;
        margin: 0 auto;
        max-width: 95%;
      }

      h1 {
        text-align: center;
        color: #333;
        margin-bottom: 30px;
        font-size: 24px;
      }

      #mindmap {
        width: 100%;
        height: 800px;
        border: 1px solid #ddd;
        border-radius: 10px;
        background: #fafafa;
      }

      .node {
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .node rect {
        fill: #fff;
        stroke: steelblue;
        stroke-width: 2px;
        rx: 15;
        ry: 15;
        transition: all 0.3s ease;
      }

      .node:hover rect {
        stroke-width: 3px;
        stroke: #ff6b6b;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
      }

      .node text {
        font: 12px "Microsoft YaHei", sans-serif;
        fill: #333;
        text-anchor: middle;
        dominant-baseline: middle;
        pointer-events: none;
      }

      .node.root rect {
        fill: #3498db;
        stroke: #2980b9;
      }

      .node.root text {
        font-size: 16px;
        font-weight: bold;
        fill: white;
      }

      .node.level-1 rect {
        fill: #e8f4fd;
        stroke: #3498db;
      }

      .node.level-1 text {
        font-size: 14px;
        font-weight: 600;
        fill: #2c3e50;
      }

      .node.level-2 rect {
        fill: #f8f9fa;
        stroke: #95a5a6;
      }

      .node.level-2 text {
        font-size: 12px;
        fill: #34495e;
      }

      .node.level-3 rect {
        fill: #ffffff;
        stroke: #bdc3c7;
      }

      .node.level-3 text {
        font-size: 11px;
        fill: #7f8c8d;
      }

      .link {
        fill: none;
        stroke: #ccc;
        stroke-width: 2px;
        transition: all 0.3s ease;
      }

      .link:hover {
        stroke: #ff6b6b;
        stroke-width: 3px;
      }

      .controls {
        text-align: center;
        margin-bottom: 20px;
      }

      .btn {
        background: #3498db;
        color: white;
        border: none;
        padding: 10px 20px;
        margin: 0 5px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.3s ease;
      }

      .btn:hover {
        background: #2980b9;
      }

      .tooltip {
        position: absolute;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 12px;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        line-height: 1.4;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🧠 AI思维导图 - 便利与风险并存的未来</h1>

      <div class="controls">
        <button class="btn" onclick="expandAll()">展开全部</button>
        <button class="btn" onclick="collapseAll()">折叠全部</button>
        <button class="btn" onclick="resetZoom()">重置视图</button>
      </div>

      <div id="mindmap"></div>
      <div class="tooltip" id="tooltip"></div>
    </div>

    <script>
      // 颜色方案
      const colors = [
        "#e74c3c",
        "#3498db",
        "#2ecc71",
        "#f39c12",
        "#9b59b6",
        "#1abc9c",
        "#e67e22",
        "#34495e",
      ];

      // 设置SVG尺寸和边距
      const margin = { top: 20, right: 120, bottom: 20, left: 120 };
      const width =
        document.getElementById("mindmap").clientWidth -
        margin.left -
        margin.right;
      const height = 800 - margin.top - margin.bottom;

      // 创建SVG
      const svg = d3
        .select("#mindmap")
        .append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom);

      const g = svg
        .append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

      // 添加缩放功能
      const zoom = d3
        .zoom()
        .scaleExtent([0.1, 3])
        .on("zoom", (event) => {
          g.attr("transform", event.transform);
        });

      svg.call(zoom);

      // 创建树布局
      const tree = d3.tree().size([height, width]);
      const tooltip = d3.select("#tooltip");

      let root, nodes, links;

      // 数据转换函数
      function transformData(data) {
        return {
          name: data.one_sentence_summary || "AI思维导图",
          children: data.children.map((item) => ({
            name: item.keypoint,
            children: item.children.map((subItem) => ({
              name: subItem.sub_keypoint.one_sentence_summary,
              detail: subItem.sub_keypoint.detail,
              children: null,
            })),
          })),
        };
      }

      // 加载数据
      d3.json("mindmap_data.json").then(function (data) {
        // 转换数据结构以适配新的JSON格式
        const transformedData = transformData(data);
        root = d3.hierarchy(transformedData);
        root.x0 = height / 2;
        root.y0 = 0;

        // 初始化：折叠除了根节点的所有节点
        root.children.forEach(collapse);

        update(root);
      });

      function update(source) {
        // 计算新的树布局
        const treeData = tree(root);
        nodes = treeData.descendants();
        links = treeData.descendants().slice(1);

        // 标准化固定深度
        nodes.forEach((d) => {
          d.y = d.depth * 180;
        });

        // 更新节点
        const node = g
          .selectAll("g.node")
          .data(nodes, (d) => d.id || (d.id = ++i));

        // 进入新节点
        const nodeEnter = node
          .enter()
          .append("g")
          .attr("class", (d) => `node level-${d.depth}`)
          .attr("transform", (d) => `translate(${source.y0},${source.x0})`)
          .on("click", click)
          .on("mouseover", showTooltip)
          .on("mouseout", hideTooltip);

        // 添加文本（先添加文本以便计算尺寸）
        nodeEnter
          .append("text")
          .attr("dy", ".35em")
          .attr("text-anchor", "middle")
          .text((d) => {
            const name = d.data.name || d.data;
            // 根据层级调整文本长度限制
            const maxLength = d.depth === 0 ? 20 : d.depth === 1 ? 18 : 15;
            return name.length > maxLength
              ? name.substring(0, maxLength) + "..."
              : name;
          })
          .style("fill-opacity", 1e-6);

        // 添加矩形背景（在文本之后添加，这样矩形在文本后面）
        nodeEnter
          .insert("rect", "text")
          .attr("width", 1e-6)
          .attr("height", 1e-6)
          .attr("x", 0)
          .attr("y", 0)
          .style("fill", (d) => (d._children ? "lightsteelblue" : "#fff"));

        // 更新现有节点
        const nodeUpdate = nodeEnter.merge(node);

        nodeUpdate
          .transition()
          .duration(750)
          .attr("transform", (d) => `translate(${d.y},${d.x})`);

        // 更新文本
        nodeUpdate.select("text").style("fill-opacity", 1);

        // 更新矩形大小以包裹文本
        nodeUpdate.select("rect").each(function (d) {
          const textElement = d3.select(this.parentNode).select("text");
          const bbox = textElement.node().getBBox();
          const padding = d.depth === 0 ? 20 : d.depth === 1 ? 16 : 12;

          d3.select(this)
            .attr("width", bbox.width + padding)
            .attr("height", bbox.height + padding)
            .attr("x", -(bbox.width + padding) / 2)
            .attr("y", -(bbox.height + padding) / 2)
            .style("fill", (d) => (d._children ? "lightsteelblue" : "#fff"));
        });

        // 移除退出的节点
        const nodeExit = node
          .exit()
          .transition()
          .duration(750)
          .attr("transform", (d) => `translate(${source.y},${source.x})`)
          .remove();

        nodeExit.select("rect").attr("width", 1e-6).attr("height", 1e-6);

        nodeExit.select("text").style("fill-opacity", 1e-6);

        // 更新链接
        const link = g.selectAll("path.link").data(links, (d) => d.id);

        const linkEnter = link
          .enter()
          .insert("path", "g")
          .attr("class", "link")
          .attr("d", (d) => {
            const o = { x: source.x0, y: source.y0 };
            return diagonal(o, o);
          });

        const linkUpdate = linkEnter.merge(link);

        linkUpdate
          .transition()
          .duration(750)
          .attr("d", (d) => diagonal(d, d.parent));

        const linkExit = link
          .exit()
          .transition()
          .duration(750)
          .attr("d", (d) => {
            const o = { x: source.x, y: source.y };
            return diagonal(o, o);
          })
          .remove();

        // 存储旧位置用于过渡
        nodes.forEach((d) => {
          d.x0 = d.x;
          d.y0 = d.y;
        });
      }

      // 创建对角线路径
      function diagonal(s, d) {
        const path = `M ${s.y} ${s.x}
                    C ${(s.y + d.y) / 2} ${s.x},
                      ${(s.y + d.y) / 2} ${d.x},
                      ${d.y} ${d.x}`;
        return path;
      }

      // 点击事件
      function click(event, d) {
        if (d.children) {
          d._children = d.children;
          d.children = null;
        } else {
          d.children = d._children;
          d._children = null;
        }
        update(d);
      }

      // 折叠节点
      function collapse(d) {
        if (d.children) {
          d._children = d.children;
          d._children.forEach(collapse);
          d.children = null;
        }
      }

      // 工具提示
      function showTooltip(event, d) {
        const name = d.data.name || d.data;
        const detail = d.data.detail;

        let content = `<strong>${name}</strong>`;
        if (detail) {
          content += `<br><br><span style="font-size: 11px; color: #ccc;">${detail}</span>`;
        }

        tooltip
          .style("opacity", 1)
          .html(content)
          .style("left", event.pageX + 10 + "px")
          .style("top", event.pageY - 10 + "px");
      }

      function hideTooltip() {
        tooltip.style("opacity", 0);
      }

      // 控制函数
      function expandAll() {
        root.descendants().forEach((d) => {
          if (d._children) {
            d.children = d._children;
            d._children = null;
          }
        });
        update(root);
      }

      function collapseAll() {
        root.descendants().forEach((d) => {
          if (d.children) {
            d._children = d.children;
            d.children = null;
          }
        });
        root.children = root._children;
        root._children = null;
        update(root);
      }

      function resetZoom() {
        svg.transition().duration(750).call(zoom.transform, d3.zoomIdentity);
      }

      let i = 0;
    </script>
  </body>
</html>
