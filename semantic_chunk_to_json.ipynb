from chonkie import RecursiveChunker, RecursiveRules
from chonkie import LateChunker, OverlapRefinery


# 初始化OverlapRefinery来添加overlap
overlap_refinery = OverlapRefinery(
    tokenizer_or_token_counter="character",  # 使用字符计数
    context_size=0.15,                       # 15%的overlap（推荐范围10-20%）
    method="suffix",                         # 从下一个chunk添加上下文到当前chunk末尾
    merge=True,                             # 直接合并到chunk.text中
    inplace=False                           # 返回新的chunk列表
)

# 获取文本
file_path = r"C:\Users\<USER>\Desktop\ai_mindmap\documents\python第八章.md"

with open(file_path, 'r', encoding='utf-8') as file:
        text = file.read()
    

from chonkie import SemanticChunker

# Basic initialization with default parameters
semantic_chunker = SemanticChunker(
    embedding_model="minishlab/potion-base-8M",  # Default model
    threshold=0.5,                               # Similarity threshold (0-1) or (1-100) or "auto"
    chunk_size=2048,                              # Maximum tokens per chunk
    min_sentences=1                              # Initial sentences per chunk
)

# 然后用semantic进行分块
chunks = semantic_chunker.chunk(text)

# 然后使用OverlapRefinery添加overlap
refined_chunks = overlap_refinery(chunks)

print(f"原始chunks数量: {len(chunks)}")
print(f"添加overlap后chunks数量: {len(refined_chunks)}")
print("\n=== 带overlap的chunks ===")

for i, chunk in enumerate(refined_chunks):
    print(f"\nChunk {i+1}:")
    print(f"Token count: {chunk.token_count}")
    print(f"Chunk Text: {chunk.text}...")

